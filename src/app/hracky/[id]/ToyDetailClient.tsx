'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import { useReservationNotificationContext } from '../../../contexts/ReservationNotificationContext';
import { translateToyStatus, getToyStatusClasses } from '../../../lib/constants';
import { formatToyPriceWithEuro } from '../../../lib/priceUtils';
import { generateToyUrl } from '../../../lib/seoUtils';

// Typy pre dáta
interface ToyOwner {
  hashedId: string; // Hashed ID from the API
  name: string;
  city: string | null;
}

interface ToyLocation {
  city: string;
  postalCode: string;
}

interface UserContact {
  id: string; // Now hashed ID as primary ID
  name: string;
  email: string;
  phone: string | null;
  address: string | null;
  city: string | null;
  postalCode: string | null;
}

interface Reservation {
  id: string; // Now hashed ID as primary ID
  hashedId: string; // Backward compatibility
  status: string;
  position: number; // Pozícia v čakacom zozname
  userId: string; // Now hashed ID as primary ID
  hashedUserId: string; // Backward compatibility
  toyId: string; // Now hashed ID as primary ID
  hashedToyId: string; // Backward compatibility
  ownerId: string; // Now hashed ID as primary ID
  hashedOwnerId: string; // Backward compatibility
}

interface WaitingListInfo {
  totalReservations: number;
  userPosition: number;
}

interface ToyDetail {
  id: string; // Hashované ID
  name: string;
  description: string;
  images: string[];
  type: string;
  typeLabel?: string; // Popisný label pre typ hračky
  location?: ToyLocation; // Lokalita hračky (z vlastníka)
  price: number;
  deposit: number;
  priceType?: string; // Typ ceny (PER_DAY alebo PER_RENTAL)
  status: string;
  statusLabel?: string; // Popisný label pre status hračky
  createdAt: string;
  updatedAt: string;
  owner: ToyOwner;
  maxReservationDays: number; // Maximálny počet dní rezervácie
  reservation?: Reservation; // Rezervácia používateľa, ak existuje
  waitingList?: WaitingListInfo; // Informácie o čakacom zozname
}

// Pomocná funkcia pre formátovanie dátumu
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('sk-SK', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

// Pomocná funkcia pre preklad typu hračky
function translateToyType(type: string): string {
  const translations: Record<string, string> = {
    'EDUCATIONAL': 'Vzdelávacie',
    'PLUSH': 'Plyšové',
    'BUILDING': 'Stavebnice',
    'ROLEPLAY': 'Na hranie rolí',
    'CARS': 'Autíčka',
    'DOLLS': 'Bábiky',
    'OUTDOOR': 'Vonkajšie',
    'BOARD_GAMES': 'Spoločenské hry',
    'ELECTRONIC': 'Elektronické',
    'OTHER': 'Iné'
  };

  return translations[type] || type;
}

interface ToyDetailClientProps {
  toyId: string;
  isLegacyUrl: boolean;
}

export default function ToyDetailClient({ toyId, isLegacyUrl }: ToyDetailClientProps) {
  const { user } = useAuth();
  const { refresh: refreshNotifications } = useReservationNotificationContext();
  const router = useRouter();
  const [toy, setToy] = useState<ToyDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [reservationStatus, setReservationStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [reservationError, setReservationError] = useState('');
  const [hideStatus, setHideStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [hideError, setHideError] = useState('');
  const [activeImage, setActiveImage] = useState(0);
  const [ownerContact, setOwnerContact] = useState<UserContact | null>(null);
  const [contactLoading, setContactLoading] = useState(false);
  const [contactError, setContactError] = useState('');

  // Načítanie detailu hračky
  useEffect(() => {
    async function loadToyDetail() {
      setLoading(true);
      try {
        // Použijeme ID z URL parametra (už je hashované alebo bude konvertované na serveri)
        // Pridáme userId do URL parametrov, ak je používateľ prihlásený
        let url = `/api/toy/${toyId}`;
        if (user && user.hashedUserId) {
          url += `?userId=${user.hashedUserId}`;
        }
        const response = await fetch(url);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Hračka nebola nájdená');
          }
          throw new Error('Nepodarilo sa načítať detail hračky');
        }

        const data: ToyDetail = await response.json();
        setToy(data);
        setError('');

        // Redirect legacy URLs to SEF URLs
        if (isLegacyUrl && data.name && data.id) {
          const sefUrl = generateToyUrl(data.name, data.id);
          router.replace(sefUrl);
          return;
        }
      } catch (err) {
        console.error('Chyba pri načítaní detailu hračky:', err);
        setError(err instanceof Error ? err.message : 'Nepodarilo sa načítať detail hračky');
        setToy(null);
      } finally {
        setLoading(false);
      }
    }

    loadToyDetail();
  }, [toyId, user]);

  // Načítanie kontaktných údajov majiteľa hračky, ak má používateľ potvrdenú rezerváciu
  useEffect(() => {
    async function loadOwnerContact() {
      if (!toy || !user || !user.hashedUserId || !toy.owner || !toy.reservation) {
        return;
      }

      // Načítame kontaktné údaje majiteľa len ak má používateľ potvrdenú rezerváciu
      if (toy.reservation.status !== 'CONFIRMED' && toy.reservation.status !== 'ACTIVE') {
        return;
      }

      setContactLoading(true);
      setContactError('');

      try {
        // Prepare headers
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
        };

        // Add authorization token if available
        if (typeof user.getIdToken === 'function') {
          headers['Authorization'] = `Bearer ${await user.getIdToken()}`;
        }

        const response = await fetch(`/api/users/contact/${toy.owner.hashedId}`, {
          headers,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Nepodarilo sa načítať kontaktné údaje majiteľa');
        }

        const contactData: UserContact = await response.json();
        setOwnerContact(contactData);
      } catch (err) {
        console.error('Chyba pri načítaní kontaktných údajov majiteľa:', err);
        setContactError(err instanceof Error ? err.message : 'Nepodarilo sa načítať kontaktné údaje majiteľa');
      } finally {
        setContactLoading(false);
      }
    }

    loadOwnerContact();
  }, [toy, user]);

  // Funkcia pre vytvorenie rezervácie
  async function handleReservation() {
    if (!user || !user.dbUserId) {
      return; // Používateľ nie je prihlásený
    }

    if (!toy) {
      return; // Nemáme dáta o hračke
    }

    setReservationStatus('loading');
    setReservationError('');

    try {
      // Vytvorenie rezervácie
      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba hashedUserId používateľa');
      }

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authorization token if available
      if (typeof user.getIdToken === 'function') {
        headers['Authorization'] = `Bearer ${await user.getIdToken()}`;
      }

      const response = await fetch('/api/reservations', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          toyId: toy.id,
          ownerId: toy.owner.hashedId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa vytvoriť rezerváciu');
      }

      // Úspešné vytvorenie rezervácie
      setReservationStatus('success');

      // Obnovenie notifikácií pre okamžitú aktualizáciu UI
      refreshNotifications();

      // Obnovenie detailu hračky po úspešnej rezervácii
      setTimeout(async () => {
        // Reload toy detail instead of full page reload
        try {
          let url = `/api/toy/${toyId}`;
          if (user && user.hashedUserId) {
            url += `?userId=${user.hashedUserId}`;
          }
          const response = await fetch(url);
          if (response.ok) {
            const data: ToyDetail = await response.json();
            setToy(data);
          }
        } catch (err) {
          console.error('Chyba pri obnovení detailu hračky:', err);
        }
        setReservationStatus('idle');
      }, 2000);
    } catch (err) {
      console.error('Chyba pri vytváraní rezervácie:', err);
      setReservationStatus('error');
      setReservationError(err instanceof Error ? err.message : 'Nepodarilo sa vytvoriť rezerváciu');
    }
  }

  // Funkcia pre skrytie hračky
  async function handleHideToy() {
    if (!user || !user.dbUserId) {
      return; // Používateľ nie je prihlásený
    }

    if (!toy) {
      return; // Nemáme dáta o hračke
    }

    // Nemôžeme skryť vlastnú hračku
    if (user.hashedUserId === toy.owner.hashedId) {
      setHideStatus('error');
      setHideError('Nemôžete skryť vlastnú hračku');
      return;
    }

    setHideStatus('loading');
    setHideError('');

    try {
      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba hashedUserId používateľa');
      }

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authorization token if available
      if (typeof user.getIdToken === 'function') {
        headers['Authorization'] = `Bearer ${await user.getIdToken()}`;
      }

      const response = await fetch('/api/toys/hide', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          toyId: toy.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa skryť hračku');
      }

      // Úspešné skrytie hračky
      setHideStatus('success');

      // Presmerovanie na zoznam hračiek po úspešnom skrytí
      setTimeout(() => {
        window.location.href = '/hracky';
      }, 2000);
    } catch (err) {
      console.error('Chyba pri skrývaní hračky:', err);
      setHideStatus('error');
      setHideError(err instanceof Error ? err.message : 'Nepodarilo sa skryť hračku');
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam detail hračky...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">{error}</p>
          <Link href="/hracky" className="btn btn-primary mt-4">
            Späť na zoznam hračiek
          </Link>
        </div>
      </div>
    );
  }

  if (!toy) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Hračka nebola nájdená</p>
          <Link href="/hracky" className="btn btn-primary mt-4">
            Späť na zoznam hračiek
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      {/* Navigácia späť */}
      <div className="mb-6">
        <Link href="/hracky" className="text-primary hover:underline flex items-center">
          <span className="mr-1">←</span> Späť na zoznam hračiek
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-24">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
          {/* Ľavá strana - obrázky */}
          <div>
            {/* Hlavný obrázok */}
            <div className="relative h-80 bg-neutral-100 rounded-lg mb-4 overflow-hidden">
              {toy.images.length > 0 ? (
                <div className="w-full h-full flex items-center justify-center">
                  <img
                    src={toy.images[activeImage]}
                    alt={toy.name}
                    className="object-contain max-h-full"
                  />
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center text-neutral-400">
                  <span className="text-7xl">🧸</span>
                </div>
              )}
            </div>

            {/* Miniatúry obrázkov */}
            {toy.images.length > 1 && (
              <div className="flex space-x-2 overflow-x-auto">
                {toy.images.map((image, index) => (
                  <div
                    key={index}
                    className={`w-20 h-20 rounded-md overflow-hidden cursor-pointer border-2 ${
                      activeImage === index ? 'border-primary' : 'border-transparent'
                    }`}
                    onClick={() => setActiveImage(index)}
                  >
                    <img
                      src={image}
                      alt={`${toy.name} - obrázok ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pravá strana - informácie */}
          <div>
            <h1 className="text-3xl font-bold mb-2">{toy.name}</h1>

            <div className="flex items-center mb-4 flex-wrap gap-2">
              <span className="bg-primary-light text-primary px-3 py-1 rounded-full text-sm font-medium">
                {toy.typeLabel || translateToyType(toy.type)}
              </span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getToyStatusClasses(toy.status)}`}>
                {toy.statusLabel || translateToyStatus(toy.status)}
              </span>
              <span className="text-neutral-500 text-sm">
                Pridané {formatDate(toy.createdAt)}
              </span>
              {user && user.hashedUserId === toy.owner.hashedId && (
                <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                  Vaša hračka
                </span>
              )}
            </div>

            <div className="mb-6">
              <p className="text-neutral-700 whitespace-pre-line">{toy.description}</p>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-neutral-50 p-3 rounded-lg">
                <p className="text-sm text-neutral-500">{toy.priceType === 'PER_RENTAL' ? 'Cena za výpožičku' : 'Cena za deň'}</p>
                <p className="text-xl font-bold text-primary">{formatToyPriceWithEuro(toy.price, toy.priceType)}</p>
              </div>
              <div className="bg-neutral-50 p-3 rounded-lg">
                <p className="text-sm text-neutral-500">Záloha</p>
                {toy.deposit === 0 ? (
                  <p className="text-green-600 bg-green-100 px-2 py-1 rounded-full inline-block mt-1">bez zálohy</p>
                ) : (
                  <p className="text-xl font-bold">{toy.deposit} €</p>
                )}
              </div>
              <div className="bg-neutral-50 p-3 rounded-lg">
                <p className="text-sm text-neutral-500">Lokalita</p>
                <p className="font-medium">{toy.location?.city || toy.owner.city || 'Neuvedené'}</p>
                <p className="text-sm text-neutral-500">{toy.location?.postalCode || ''}</p>
              </div>
              <div className="bg-neutral-50 p-3 rounded-lg">
                <p className="text-sm text-neutral-500">Stav</p>
                <span className={`inline-block px-2 py-1 rounded-full text-sm font-medium ${getToyStatusClasses(toy.status)}`}>
                  {toy.statusLabel || translateToyStatus(toy.status)}
                </span>
              </div>
              <div className="bg-neutral-50 p-3 rounded-lg col-span-2">
                <p className="text-sm text-neutral-500">Maximálna doba rezervácie</p>
                <p className="font-medium">{toy.maxReservationDays} {toy.maxReservationDays === 1 ? 'deň' : toy.maxReservationDays < 5 ? 'dni' : 'dní'}</p>
              </div>
            </div>

            {/* Tlačidlá pre akcie */}
            {user && user.hashedUserId === toy.owner.hashedId ? (
              // Tlačidlo pre úpravu hračky (pre vlastníka)
              <Link href={`/moje-hracky/nova?id=${toy.id}`} className="btn btn-primary w-full mb-4">
                Upraviť hračku
              </Link>
            ) : (
              // Tlačidlá pre rezerváciu a skrytie (pre ostatných používateľov)
              <div>
                {/* Zobrazenie informácie o rezervácii používateľa */}
                {toy.reservation ? (
                  <div className="bg-blue-50 p-3 rounded-lg mb-3 text-sm">
                    <p className="font-medium text-blue-800">
                      {toy.reservation.status === 'PENDING' && toy.reservation.position === 0 && 'Máte čakajúcu rezerváciu na túto hračku.'}
                      {toy.reservation.status === 'PENDING' && toy.reservation.position > 0 && `Ste ${toy.reservation.position + 1}. v poradí na túto hračku.`}
                      {toy.reservation.status === 'CONFIRMED' && 'Máte potvrdenú rezerváciu na túto hračku.'}
                      {toy.reservation.status === 'ACTIVE' && 'Máte aktívnu výpožičku tejto hračky.'}
                    </p>
                    {toy.waitingList && toy.waitingList.totalReservations > 1 && (
                      <p className="text-blue-600 mt-1">
                        Celkový počet záujemcov: {toy.waitingList.totalReservations}
                      </p>
                    )}
                  </div>
                ) : (
                  <button
                    className="btn btn-primary w-full mb-2"
                    disabled={!user || reservationStatus === 'loading'}
                    onClick={handleReservation}
                  >
                    {reservationStatus === 'loading' ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Rezervujem...
                      </span>
                    ) : (
                      toy.status === 'AVAILABLE' ? 'Rezervovať hračku' :
                      toy.status === 'RESERVED' ? 'Zaradiť sa do čakacieho zoznamu' :
                      'Momentálne nedostupné'
                    )}
                  </button>
                )}

                {/* Tlačidlo pre skrytie hračky - skryté pre vlastníkov hračky */}
                {user && user.hashedUserId !== toy.owner.hashedId && (
                  <button
                    className="btn btn-outline w-full mb-2"
                    disabled={hideStatus === 'loading'}
                    onClick={handleHideToy}
                  >
                    {hideStatus === 'loading' ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Skrývam...
                      </span>
                    ) : 'Skryť túto hračku'}
                  </button>
                )}

                {!user && (
                  <p className="text-sm text-neutral-500 text-center mb-4">
                    Pre rezervovanie hračky sa musíte najprv <Link href="/login" className="text-primary hover:underline">prihlásiť</Link>
                  </p>
                )}

                {reservationStatus === 'success' && (
                  <div className="bg-green-100 text-green-800 p-3 rounded-md mb-4 text-sm">
                    Rezervácia bola úspešne vytvorená. Stránka sa obnoví.
                  </div>
                )}

                {reservationStatus === 'error' && (
                  <div className="bg-red-100 text-red-800 p-3 rounded-md mb-4 text-sm">
                    {reservationError || 'Nastala chyba pri vytváraní rezervácie.'}
                  </div>
                )}

                {hideStatus === 'success' && (
                  <div className="bg-green-100 text-green-800 p-3 rounded-md mb-4 text-sm">
                    Hračka bola úspešne skrytá. Budete presmerovaný na zoznam hračiek.
                  </div>
                )}

                {hideStatus === 'error' && (
                  <div className="bg-red-100 text-red-800 p-3 rounded-md mb-4 text-sm">
                    {hideError || 'Nastala chyba pri skrývaní hračky.'}
                  </div>
                )}
              </div>
            )}

            {/* Informácie o majiteľovi */}
            <div className="border-t border-neutral-200 pt-4 mt-4">
              <h2 className="text-lg font-semibold mb-2">Majiteľ hračky</h2>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{toy.owner.name}</p>
                  <p className="text-sm text-neutral-500">
                    {toy.owner.city || 'Lokalita neuvedená'}
                  </p>
                </div>
                {toy.owner && toy.owner.hashedId ? (
                  <Link
                    href={`/profil/${toy.owner.hashedId}`}
                    className="btn btn-outline text-sm py-1"
                  >
                    Zobraziť všetky hračky od tohto používateľa
                  </Link>
                ) : (
                  <span className="text-neutral-500 text-sm">Informácie o používateľovi nie sú dostupné</span>
                )}
              </div>

              {/* Kontaktné údaje majiteľa - zobrazí sa len ak má používateľ potvrdenú rezerváciu */}
              {toy.reservation && (toy.reservation.status === 'CONFIRMED' || toy.reservation.status === 'ACTIVE') && (
                <div className="mt-4 bg-green-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-green-800 mb-2">Kontaktné údaje majiteľa</h3>

                  {contactLoading && (
                    <p className="text-sm text-neutral-500">Načítavam kontaktné údaje...</p>
                  )}

                  {contactError && (
                    <p className="text-sm text-red-600">{contactError}</p>
                  )}

                  {ownerContact && (
                    <div className="text-sm">
                      <p><span className="font-medium">Meno:</span> {ownerContact.name}</p>
                      <p><span className="font-medium">Email:</span> {ownerContact.email}</p>
                      {ownerContact.phone && (
                        <p><span className="font-medium">Telefón:</span> {ownerContact.phone}</p>
                      )}
                      {ownerContact.address && (
                        <p><span className="font-medium">Adresa:</span> {ownerContact.address}</p>
                      )}
                      {ownerContact.city && (
                        <p><span className="font-medium">Mesto:</span> {ownerContact.city}</p>
                      )}
                      {ownerContact.postalCode && (
                        <p><span className="font-medium">PSČ:</span> {ownerContact.postalCode}</p>
                      )}
                    </div>
                  )}

                  <p className="text-xs text-green-700 mt-2">
                    Tieto kontaktné údaje sú dostupné, pretože máte potvrdenú rezerváciu tejto hračky.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
