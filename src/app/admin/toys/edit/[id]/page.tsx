'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../../../../contexts/AuthContext';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { toNumericId } from '../../../../../lib/hashUtils';
import CloudinaryUpload from '../../../../../components/CloudinaryUpload';
import ToyStatusSelect from '../../../../../components/ToyStatusSelect';
import CustomSelect from '../../../../../components/CustomSelect';
import LoadingOverlay from '../../../../../components/LoadingOverlay';
import LoadingButton from '../../../../../components/LoadingButton';

// Typy pre formulár
interface ToyFormData {
  name: string;
  description: string;
  type: string;
  price: string;
  deposit: string;
  priceType: string;
  status: string;
  maxReservationDays: number;
  images: string[];
}

// Typy pre filtre
interface ToyType {
  value: string;
  label: string;
}

interface Filters {
  toyTypes: ToyType[];
}

export default function EditToy() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const hashedToyId = params.id as string;
  const toyId = hashedToyId ? toNumericId(hashedToyId) : null;

  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [filters, setFilters] = useState<Filters>({
    toyTypes: []
  });

  // Formulárové dáta
  const [formData, setFormData] = useState<ToyFormData>({
    name: '',
    description: '',
    type: '',
    price: '',
    deposit: '',
    priceType: 'PER_DAY', // Predvolená hodnota je cena za deň
    // locationId už nie je potrebné, používame lokalitu používateľa
    status: 'DRAFT',
    maxReservationDays: 7, // Predvolená hodnota je 7 dní
    images: []
  });

  // Presmerovanie na domovskú stránku, ak používateľ nie je admin
  useEffect(() => {
    if (user === null) {
      // Používateľ nie je prihlásený
      router.push('/prihlasenie');
    } else if (user && user.role !== 'admin') {
      // Používateľ je prihlásený, ale nie je admin
      router.push('/');
    }
  }, [user, router]);

  // Načítanie filtrov a existujúcej hračky
  useEffect(() => {
    async function loadData() {
      if (!user || !user.dbUserId) {
        setIsLoading(false);
        return;
      }

      try {
        // Načítanie typov hračiek
        const typesResponse = await fetch('/api/filters');
        if (!typesResponse.ok) {
          throw new Error('Nepodarilo sa načítať typy hračiek');
        }
        const typesData = await typesResponse.json();

        setFilters({
          toyTypes: typesData.toyTypes.filter((type: any) => type.value !== 'Všetky typy')
        });

        // Ak máme ID hračky, načítame existujúcu hračku
        if (toyId) {
          if (!user.getIdToken) return;
          const token = await user.getIdToken();
          const toyResponse = await fetch(`/api/toys/${hashedToyId}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
            }
          });

          if (!toyResponse.ok) {
            throw new Error('Nepodarilo sa načítať hračku');
          }

          const toyData = await toyResponse.json();

          // Filtrujeme placeholder obrázky
          const filteredImages = (toyData.images || []).filter((img: string) => !img.includes('/toys/placeholder.jpg') && !img.includes('placeholder.jpg'));

          // Nastavenie formulárových dát
          setFormData({
            name: toyData.name,
            description: toyData.description,
            type: toyData.type,
            price: toyData.price.toString(),
            deposit: toyData.deposit.toString(),
            priceType: toyData.priceType || 'PER_DAY',
            // locationId už nie je potrebné, používame lokalitu používateľa
            status: toyData.status || 'DRAFT',
            maxReservationDays: toyData.maxReservationDays || 7,
            images: filteredImages
          });
        }
      } catch (err) {
        console.error('Chyba pri načítaní dát:', err);
        setError(err instanceof Error ? err.message : 'Nepodarilo sa načítať potrebné údaje');
      } finally {
        setIsLoading(false);
      }
    }

    if (user) {
      loadData();
    } else {
      setIsLoading(false);
    }
  }, [user, toyId]);

  // Spracovanie zmien vo formulári
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Memoizovaný handler pre zmenu obrázkov
  const handleImagesChange = useCallback((images: string[]) => {
    setFormData(prev => ({ ...prev, images }));
  }, []);

  // Odoslanie formulára
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !user.dbUserId) {
      setError('Nie ste prihlásený');
      return;
    }

    // Validácia formulára
    if (!formData.name || !formData.description || !formData.type || !formData.price || !formData.deposit || !formData.priceType) {
      setError('Vyplňte všetky povinné polia');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user.getIdToken) {
        setError('Chyba pri získavaní autentifikačného tokenu');
        return;
      }
      const token = await user.getIdToken();

      // Aktualizácia hračky cez admin endpoint
      const response = await fetch('/api/admin/toys/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          id: hashedToyId,
          ...formData,
          // Pridanie placeholder obrázka, ak nie sú zadané žiadne obrázky
          // Filtrujeme placeholder obrázky, ak sú v zozname
          images: formData.images.filter(img => !img.includes('/toys/placeholder.jpg')).length > 0
            ? formData.images.filter(img => !img.includes('/toys/placeholder.jpg'))
            : ['/toys/placeholder.jpg']
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa aktualizovať hračku');
      }

      const data = await response.json();
      setSuccess('Hračka bola úspešne aktualizovaná');

      // Presmerovanie na stránku s hračkami po 2 sekundách
      setTimeout(() => {
        router.push('/admin/toys');
      }, 2000);
    } catch (err) {
      console.error('Chyba pri aktualizácii hračky:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri aktualizácii hračky');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      <div className="mb-8">
        <Link href="/admin/toys" className="text-primary hover:underline flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Späť na zoznam hračiek
        </Link>
      </div>

      <h1 className="text-3xl font-bold mb-6">Upraviť hračku</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {/* Loading Overlay */}
      <LoadingOverlay
        isVisible={isSubmitting}
        message="Ukladám zmeny hračky..."
      />

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm p-6 mb-24">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Názov hračky */}
          <div className="col-span-2">
            <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
              Názov hračky *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full"
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Popis hračky */}
          <div className="col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-neutral-700 mb-1">
              Popis hračky *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={4}
              className="w-full"
              required
              disabled={isSubmitting}
            ></textarea>
          </div>

          {/* Typ hračky */}
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-neutral-700 mb-1">
              Typ hračky *
            </label>
            <CustomSelect
              id="type"
              options={filters.toyTypes}
              value={formData.type}
              onChange={(value) => setFormData({...formData, type: value})}
              placeholder="Vyberte typ hračky"
              disabled={isSubmitting}
            />
          </div>

         {/* Cena */}
          <div>
            <label htmlFor="price" className="block text-sm font-medium text-neutral-700 mb-1">
              Cena (€) *
            </label>
            <input
              type="number"
              id="price"
              name="price"
              value={formData.price}
              onChange={handleChange}
              min="0"
              step="0.01"
              className="w-full"
              required
              placeholder="Zadajte cenu alebo 0 pre dohodou"
              disabled={isSubmitting}
            />
            <p className="text-xs text-neutral-500 mt-1">
              💡 Tip: Ak zadáte 0 €, používateľom sa zobrazí "Dohodou" namiesto ceny
            </p>
          </div>

          {/* Typ ceny */}
          <div>
            <label htmlFor="priceType" className="block text-sm font-medium text-neutral-700 mb-1">
              Typ ceny *
            </label>
            <CustomSelect
              id="priceType"
              options={[
                { value: "PER_DAY", label: "Cena za deň" },
                { value: "PER_RENTAL", label: "Cena za výpožičku" }
              ]}
              value={formData.priceType}
              onChange={(value) => setFormData({...formData, priceType: value})}
              placeholder="Vyberte typ ceny"
              disabled={isSubmitting}
            />
            <p className="text-xs text-neutral-500 mt-1">
              Vyberte, či je cena za jeden deň alebo za celú výpožičku.
            </p>
          </div>

          {/* Záloha */}
          <div>
            <label htmlFor="deposit" className="block text-sm font-medium text-neutral-700 mb-1">
              Záloha (€) *
            </label>
            <input
              type="number"
              id="deposit"
              name="deposit"
              value={formData.deposit}
              onChange={handleChange}
              min="0"
              step="0.01"
              className="w-full"
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Status hračky */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-neutral-700 mb-1">
              Status hračky *
            </label>
            <ToyStatusSelect
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full"
              required
              disabled={isSubmitting}
            />
            <p className="text-xs text-neutral-500 mt-1">
              Nepublikovaná hračka nebude viditeľná pre ostatných používateľov.
            </p>
          </div>

          {/* Maximálny počet dní rezervácie */}
          <div>
            <label htmlFor="maxReservationDays" className="block text-sm font-medium text-neutral-700 mb-1">
              Maximálny počet dní rezervácie *
            </label>
            <CustomSelect
              id="maxReservationDays"
              options={[
                { value: "3", label: "3 dni" },
                { value: "7", label: "7 dní (týždeň)" },
                { value: "14", label: "14 dní (dva týždne)" },
                { value: "30", label: "30 dní (mesiac)" }
              ]}
              value={formData.maxReservationDays.toString()}
              onChange={(value) => setFormData({...formData, maxReservationDays: parseInt(value)})}
              placeholder="Vyberte počet dní"
              disabled={isSubmitting}
            />
            <p className="text-xs text-neutral-500 mt-1">
              Maximálna doba, na ktorú si môže používateľ rezervovať túto hračku.
            </p>
          </div>

          {/* Fotografie hračky */}
          <div className="col-span-2 mt-6">
            <CloudinaryUpload
              images={formData.images}
              onChange={handleImagesChange}
              toyId={hashedToyId}
            />
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <Link
            href="/admin/toys"
            className={`btn btn-outline mr-2 ${isSubmitting ? 'pointer-events-none opacity-50' : ''}`}
          >
            Zrušiť
          </Link>
          <LoadingButton
            type="submit"
            isLoading={isSubmitting}
            loadingText="Ukladám..."
            className="btn btn-primary"
          >
            Uložiť zmeny
          </LoadingButton>
        </div>
      </form>
    </div>
  );
}
