import { NextRequest, NextResponse } from 'next/server';
import cloudinary, { CLOUDINARY_FOLDER } from '../../../../lib/cloudinary';
import { withAuth } from '../../../../lib/auth';
import { generateUniqueFileHash, generateSecureFilename, toNumericId, isHashedId } from '../../../../lib/hashUtils';
import { prisma } from '../../../../lib/db';
import { validateFileUpload, generateSecureFileHash, sanitizeText } from '../../../../lib/inputSanitization';
import { processImage, isSupportedImageFormat, getFileExtension, getCompressionRatio } from '../../../../lib/imageProcessing';

/**
 * Handler pre nahrávanie obrázkov na Cloudinary cez backend (signed upload)
 * T<PERSON>to funk<PERSON> z<PERSON>zpečí, že obr<PERSON><PERSON><PERSON> bud<PERSON> nahr<PERSON> be<PERSON>ý<PERSON> spôsobom s podpisom
 */
async function handleImageUpload(request: NextRequest, user: any) {
  try {
    // Kontrola, či je cloudinary inicializovaný
    if (!cloudinary) {
      return NextResponse.json(
        { error: 'Cloudinary nie je inicializovaný' },
        { status: 500 }
      );
    }

    // Získanie formData z requestu s error handling pre veľké súbory
    let formData;
    try {
      formData = await request.formData();
    } catch (error) {
      console.error('Error parsing form data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Check if it's a size limit error
      if (errorMessage.includes('size') || errorMessage.includes('large') || errorMessage.includes('limit')) {
        return NextResponse.json(
          {
            error: 'Súbor je príliš veľký pre server',
            details: ['Skúste komprimovať obrázok pred nahrávaním alebo použite menší súbor.']
          },
          { status: 413 }
        );
      }

      return NextResponse.json(
        {
          error: 'Nepodarilo sa spracovať nahrávaný súbor',
          details: [errorMessage]
        },
        { status: 400 }
      );
    }

    // Získanie súboru z formData
    const file = formData.get('file') as File;

    // Debug logging
    console.log('File upload attempt:', {
      name: file?.name,
      type: file?.type,
      size: file?.size,
      lastModified: file?.lastModified
    });

    if (!file) {
      return NextResponse.json(
        {
          error: 'Neplatný súbor',
          details: ['Súbor nebol nájdený v requeste']
        },
        { status: 400 }
      );
    }

    // Rozšírená validácia súboru s podporou HEIC/HEIF a relaxed filename validation
    // Zvýšený limit pre súbory, ktoré už prešli klientskou kompresiou
    const maxSize = file.name.includes('compressed') || file.size < 3 * 1024 * 1024 ? 10 * 1024 * 1024 : 5 * 1024 * 1024;

    const fileValidation = validateFileUpload(file, {
      allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/jpg', 'image/webp', 'image/heic', 'image/heif'],
      maxSize: maxSize,
      minSize: 1024, // 1KB
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'heic', 'heif'],
      relaxedFilenameValidation: true // Allow special characters in filenames
    });

    console.log('File validation result:', {
      isValid: fileValidation.isValid,
      errors: fileValidation.errors
    });

    if (!fileValidation.isValid) {
      return NextResponse.json(
        {
          error: 'Neplatný súbor',
          details: fileValidation.errors
        },
        { status: 400 }
      );
    }

    // Dodatočná kontrola podporovaných formátov
    if (!isSupportedImageFormat(file)) {
      return NextResponse.json(
        {
          error: 'Nepodporovaný formát obrázku',
          details: ['Podporované formáty: JPG, PNG, GIF, WebP, HEIC, HEIF']
        },
        { status: 400 }
      );
    }

    // Získanie ID hračky (ak existuje) a jeho sanitizácia
    const rawToyId = formData.get('toyId') as string;
    const toyId = rawToyId ? sanitizeText(rawToyId, { maxLength: 20, allowSpecialChars: false }).sanitizedValue : null;

    // Konverzia súboru na ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    const originalBuffer = Buffer.from(arrayBuffer);
    const originalSize = originalBuffer.length;

    console.log(`Processing image: ${file.name}, original size: ${Math.round(originalSize / 1024)}KB, type: ${file.type}`);

    // Spracovanie obrázku (konverzia HEIC, zmena veľkosti, WebP konverzia)
    let processedImage;
    try {
      processedImage = await processImage(originalBuffer, file.type, {
        maxDimension: 1024,
        quality: 85,
        format: 'webp',
        maintainAspectRatio: true
      }, file.name);
    } catch (error) {
      console.error('Error processing image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Obrázok sa nepodarilo spracovať. Skúste iný súbor.';
      return NextResponse.json(
        {
          error: 'Nepodarilo sa spracovať obrázok',
          details: [errorMessage]
        },
        { status: 400 }
      );
    }

    const compressionRatio = getCompressionRatio(originalSize, processedImage.size);
    console.log(`Image processed: ${processedImage.width}x${processedImage.height}, size: ${Math.round(processedImage.size / 1024)}KB, compression: ${compressionRatio}%`);

    // Vytvoríme unikátny identifikátor používateľa pre použitie v hashovaní
    const userId = user.hashedUserId || user.dbUserId || '';

    // Vygenerujeme bezpečný hashovaný názov súboru
    const fileExtension = getFileExtension(processedImage.format);
    const secureFilename = generateSecureFilename(file.name, userId, fileExtension);

    console.log('Generated secure filename:', {
      original: file.name,
      hashed: secureFilename.hashedFilename,
      publicId: secureFilename.publicId,
      fullPath: secureFilename.fullPath
    });

    // Nahratie spracovaného obrázku na Cloudinary s bezpečným hashovaným názvom
    const uploadResult = await new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder: CLOUDINARY_FOLDER,
          public_id: secureFilename.publicId,
          resource_type: 'image',
          format: fileExtension,
          // Cloudinary optimizations
          quality: 'auto:good',
          fetch_format: 'auto',
          flags: 'progressive',
          // Metadata
          context: {
            original_filename: file.name,
            original_format: processedImage.originalFormat,
            original_size: originalSize.toString(),
            processed_size: processedImage.size.toString(),
            compression_ratio: compressionRatio.toString(),
            hashed_filename: secureFilename.hashedFilename,
            user_id: userId.toString()
          }
        },
        (error: any, result: any) => {
          if (error) {
            reject(error);
          } else {
            resolve(result);
          }
        }
      );

      // Zápis spracovaného bufferu do streamu
      uploadStream.write(processedImage.buffer);
      uploadStream.end();
    });

    // Typovanie výsledku
    const result = uploadResult as any;

    // Ak máme ID hračky, uložíme obrázok do databázy
    if (toyId) {
      try {
        // Konverzia hashu na číselné ID, ak je to potrebné
        const numericToyId = typeof toyId === 'string' ? toNumericId(toyId) : parseInt(toyId);

        if (numericToyId === null) {
          return NextResponse.json(
            { error: 'Neplatné ID hračky' },
            { status: 400 }
          );
        }

        // Kontrola, či hračka existuje
        const existingToy = await prisma.toy.findUnique({
          where: { id: numericToyId },
        });

        if (!existingToy) {
          return NextResponse.json(
            { error: 'Hračka nebola nájdená' },
            { status: 404 }
          );
        }

        // Kontrola, či používateľ je vlastníkom hračky alebo má rolu ADMIN
        if (existingToy.userId !== user.id && user.role !== 'ADMIN') {
          return NextResponse.json(
            { error: 'Nemáte oprávnenie pridať obrázok k tejto hračke' },
            { status: 403 }
          );
        }

        // Uloženie obrázku do databázy s rozšírenými metadátami
        const toyImage = await prisma.toyImage.create({
          data: {
            url: result.secure_url,
            toyId: numericToyId,
            originalFilename: file.name,
            hashedFilename: secureFilename.hashedFilename,
            fileFormat: fileExtension,
          },
        });

        return NextResponse.json({
          message: 'Obrázok bol úspešne nahraný a uložený',
          imageUrl: result.secure_url,
          toyImage,
          processing: {
            originalFormat: processedImage.originalFormat,
            finalFormat: processedImage.format,
            originalSize: Math.round(originalSize / 1024),
            finalSize: Math.round(processedImage.size / 1024),
            compressionRatio: compressionRatio,
            dimensions: `${processedImage.width}x${processedImage.height}`
          },
          security: {
            originalFilename: file.name,
            hashedFilename: secureFilename.hashedFilename,
            publicId: secureFilename.publicId
          }
        });
      } catch (error) {
        console.error('Chyba pri ukladaní obrázku:', error);
        // Pokračujeme a vrátime aspoň URL obrázku
      }
    }

    // Vrátime URL nahraného obrázku s informáciami o spracovaní
    return NextResponse.json({
      message: 'Obrázok bol úspešne nahraný',
      imageUrl: result.secure_url,
      processing: {
        originalFormat: processedImage.originalFormat,
        finalFormat: processedImage.format,
        originalSize: Math.round(originalSize / 1024),
        finalSize: Math.round(processedImage.size / 1024),
        compressionRatio: compressionRatio,
        dimensions: `${processedImage.width}x${processedImage.height}`
      },
      security: {
        originalFilename: file.name,
        hashedFilename: secureFilename.hashedFilename,
        publicId: secureFilename.publicId
      }
    });
  } catch (error) {
    console.error('Chyba pri nahrávaní obrázku:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri nahrávaní obrázku' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export const POST = withAuth(handleImageUpload);
