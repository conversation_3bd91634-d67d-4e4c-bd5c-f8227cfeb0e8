import { NextRequest, NextResponse } from 'next/server';
import { createToy, prisma } from '../../../../lib/db';
import { withAuthAndCsrf } from '../../../../lib/auth';
import { hashId } from '../../../../lib/hashUtils';

// Funkcia pre inicializáciu novej hračky s minimálnymi údajmi
async function handleInitToy(request: NextRequest, user: any) {
  try {
    // Kontrola, či je používateľ prihlásený
    if (!user || !user.id) {
      return NextResponse.json(
        { error: 'Nie ste prihlásený' },
        { status: 401 }
      );
    }

    // Lokalita sa už nepoužíva, používame lokalitu používateľa

    // Vytvorenie hračky s minimálnymi údajmi
    const toy = await createToy({
      name: 'Nová hračka',
      description: '<PERSON><PERSON> h<PERSON>',
      type: 'OTHER', // Predvolený typ
      price: 0,
      deposit: 0,
      priceType: 'PER_DAY', // Predvolený typ ceny
      status: 'DRAFT', // Nastavíme ako koncept, kým nie je publikovaná
      userId: user.id, // Používame ID z autentifikovaného používateľa
      // locationId už nie je potrebné, používame lokalitu používateľa
    });

    // Vrátime ID vytvorenej hračky
    return NextResponse.json({
      id: toy.id,
      hashedId: hashId(toy.id),
      message: 'Hračka bola úspešne inicializovaná'
    });
  } catch (error) {
    console.error('Chyba pri inicializácii hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri inicializácii hračky' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie a CSRF ochranou
export const POST = withAuthAndCsrf(handleInitToy);
