import { NextRequest, NextResponse } from 'next/server';
import {
  getAllToys,
  getAllToysForAdmin,
  getToysByType,
  getToysByLocation,
  getToysByCityAndRadius,
  searchToys,
  getUserToys
} from '../../../lib/db';
import { prisma } from '../../../lib/db';
import { toNumericId, isHashedId, hashId } from '../../../lib/hashUtils';
import { isPointWithinRadius, calculateDistance } from '../../../lib/geoUtils';
import { verifyAuth } from '../../../lib/auth';
import {
  sanitizeSearchQuery,
  sanitizeText,
  sanitizeNumericInput,
  sanitizeCoordinates,
  sanitizeApiParams
} from '../../../lib/inputSanitization';
import { anonymizeName } from '../../../lib/nameUtils';

// Definícia typov
interface ToyImage {
  id: number;
  url: string;
  toyId: number;
  createdAt: Date;
}

interface User {
  id: number;
  name: string;
  email: string;
  status: string;
  city?: string | null;
  postalCode?: string | null;
  address?: string | null;
  latitude?: number | null;
  longitude?: number | null;
}

interface Toy {
  id: number;
  name: string;
  description: string;
  type: string;
  price: number;
  deposit: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  userId: number;
  images: ToyImage[];
  user: User;
  owner?: {
    id: number;
    name: string;
    email: string;
    status: string;
  };
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;

    // Sanitizácia všetkých URL parametrov
    const rawParams = {
      type: searchParams.get('type'),
      types: searchParams.getAll('types'),
      location: searchParams.get('location'),
      search: searchParams.get('search'),
      userId: searchParams.get('userId'),
      radius: searchParams.get('radius'),
      lat: searchParams.get('lat'),
      lon: searchParams.get('lon'),
      sortByDistance: searchParams.get('sortByDistance')
    };

    // Sanitizácia textových parametrov
    const type = rawParams.type ? sanitizeText(rawParams.type, { maxLength: 50, allowSpecialChars: false, allowSlovakDiacritics: true }).sanitizedValue : null;
    const types = rawParams.types.map(t => sanitizeText(t, { maxLength: 50, allowSpecialChars: false, allowSlovakDiacritics: true }).sanitizedValue).filter(Boolean);
    const location = rawParams.location ? sanitizeText(rawParams.location, { maxLength: 100, allowSpecialChars: false, allowSlovakDiacritics: true }).sanitizedValue : null;

    // Sanitizácia vyhľadávacieho dotazu
    const searchResult = sanitizeSearchQuery(rawParams.search || '', { maxLength: 100, preventSqlInjection: true, allowSlovakDiacritics: true });
    const search = searchResult.sanitizedValue;

    // Sanitizácia userId
    const userId = rawParams.userId ? sanitizeText(rawParams.userId, { maxLength: 20, allowSpecialChars: false }).sanitizedValue : null;

    // Sanitizácia číselných parametrov
    const radiusResult = sanitizeNumericInput(rawParams.radius, { min: 0, max: 1000, decimals: 1 });
    const radius = radiusResult.isValid ? radiusResult.sanitizedValue : 0;

    // Sanitizácia súradníc
    let latitude: number | undefined;
    let longitude: number | undefined;

    if (rawParams.lat && rawParams.lon) {
      const coordsResult = sanitizeCoordinates(rawParams.lat, rawParams.lon, { validateRange: true, precision: 6 });
      if (coordsResult.isValid) {
        latitude = coordsResult.sanitizedValue.latitude;
        longitude = coordsResult.sanitizedValue.longitude;
      }
    }

    // Parameter for sorting by distance
    const sortByDistance = rawParams.sortByDistance === 'true';

    // Overenie autentifikácie používateľa
    const authenticatedUser = await verifyAuth(request);
    const isAdmin = authenticatedUser ? authenticatedUser.role === 'ADMIN' : false;

    let toys: any[];

    // Ak je zadané userId, získame hračky konkrétneho používateľa
    if (userId) {
      let numericUserId: number | null = null;

      // Kontrola, či je userId hashované
      if (isHashedId(userId)) {
        numericUserId = toNumericId(userId);
      } else {
        console.error('Neplatný formát userId v parametri:', userId);
        numericUserId = null;
      }

      if (numericUserId !== null) {
        // Získame ID prihláseného používateľa pre filtrovanie skrytých hračiek a DRAFT hračiek
        const loggedInUserId = authenticatedUser ? authenticatedUser.id : undefined;
        toys = await getUserToys(numericUserId, loggedInUserId);
      } else {
        // Ak sa nepodarilo konvertovať userId, vrátime prázdne pole
        toys = [];
      }
    } else if (search) {
      // Získame ID prihláseného používateľa pre filtrovanie skrytých hračiek
      const loggedInUserId = authenticatedUser ? authenticatedUser.id : undefined;
      toys = await searchToys(search, loggedInUserId);
    } else if (types && types.length > 0) {
      // Handle multiple toy types
      // Získame ID prihláseného používateľa pre filtrovanie skrytých hračiek
      const loggedInUserId = authenticatedUser ? authenticatedUser.id : undefined;
      // First, get all toys
      const allToys = await getAllToys(loggedInUserId);
      // Then filter by the selected types
      toys = allToys.filter(toy => types.includes(toy.type));
    } else if (type && type !== 'Všetky typy') {
      // For backward compatibility
      // Získame ID prihláseného používateľa pre filtrovanie skrytých hračiek
      const loggedInUserId = authenticatedUser ? authenticatedUser.id : undefined;
      toys = await getToysByType(type, loggedInUserId);
    } else if (location && location !== 'Všetky lokality') {
      // Získame ID prihláseného používateľa pre filtrovanie skrytých hračiek
      const loggedInUserId = authenticatedUser ? authenticatedUser.id : undefined;
      // Use the new radius-based filtering if radius is provided
      if (radius > 0 && latitude !== undefined && longitude !== undefined) {
        // Get toys by city and radius
        toys = await getToysByCityAndRadius(location, radius, latitude, longitude, loggedInUserId);

        // Filter toys by distance in the application
        toys = toys.filter(toy => {
          const userLat = toy.user?.latitude;
          const userLon = toy.user?.longitude;

          // Skip toys without coordinates
          if (userLat === null || userLat === undefined || userLon === null || userLon === undefined) {
            return false;
          }

          // Check if the toy is within the radius
          return isPointWithinRadius(latitude, longitude, userLat, userLon, radius);
        });
      } else {
        // Use the original city-based filtering
        toys = await getToysByLocation(location, loggedInUserId);
      }
    } else {
      // Získame ID prihláseného používateľa pre filtrovanie skrytých hračiek
      const loggedInUserId = authenticatedUser ? authenticatedUser.id : undefined;

      // Aj pre administrátora filtrujeme hračky v stave DRAFT na stránke s hračkami
      // Administrátor môže vidieť všetky hračky vrátane DRAFT len v administrácii
      toys = await getAllToys(loggedInUserId);
    }

    // Get toy type labels from database
    const toyTypes = await prisma.toyType.findMany({
      select: {
        name: true,
        label: true
      }
    });

    // Get toy status labels from database
    const toyStatuses = await prisma.toyStatus.findMany({
      select: {
        name: true,
        label: true
      }
    });

    // Create a map of toy type names to labels
    const toyTypeLabels: { [key: string]: string } = {};
    toyTypes.forEach(type => {
      toyTypeLabels[type.name] = type.label || type.name;
    });

    // Create a map of toy status names to labels
    const toyStatusLabels: { [key: string]: string } = {};
    toyStatuses.forEach(status => {
      toyStatusLabels[status.name] = status.label || status.name;
    });

    // Transformácia dát do formátu, ktorý očakáva frontend
    const transformedToys = await Promise.all(toys.map(async (toy: Toy) => {
      // Základné informácie o hračke
      const transformedToy = {
        id: hashId(toy.id), // Používame hashované ID ako primárne ID
        name: toy.name,
        description: toy.description,
        image: toy.images && toy.images.length > 0 ? toy.images[0].url : '/toys/placeholder.jpg',
        type: toy.type,
        typeLabel: toyTypeLabels[toy.type] || toy.type, // Add the type label
        location: toy.user?.city || 'Neuvedené',
        price: toy.price,
        deposit: toy.deposit,
        status: toy.status,
        statusLabel: toyStatusLabels[toy.status] || toy.status, // Add the status label
        owner: {
          hashedId: toy.user?.id ? hashId(toy.user.id) : '',
          name: anonymizeName(toy.user?.name || 'Neznámy používateľ'),
          city: toy.user?.city || null
        } as any, // Pridáme základné informácie o vlastníkovi pre všetky hračky
        distance: undefined as number | undefined // Pridáme vzdialenosť pre zoradenie
      };

      // Ak je používateľ administrátor, získame aj informácie o vlastníkovi
      if (isAdmin && toy.userId) {
        try {
          // Získanie informácií o používateľovi z databázy
          const user = await prisma.user.findUnique({
            where: { id: toy.userId },
            select: {
              id: true,
              name: true,
              email: true,
              status: true
            }
          });

          if (user) {
            transformedToy.owner = {
              hashedId: hashId(user.id),
              name: user.name,
              email: user.email,
              status: user.status
            };
          }
        } catch (error) {
          console.error(`Chyba pri získavaní informácií o vlastníkovi hračky ${toy.id}:`, error);
        }
      }

      // Ak máme súradnice používateľa a hračka má súradnice, vypočítame vzdialenosť
      if (sortByDistance && latitude !== undefined && longitude !== undefined &&
          toy.user?.latitude !== null && toy.user?.latitude !== undefined &&
          toy.user?.longitude !== null && toy.user?.longitude !== undefined) {
        transformedToy.distance = isPointWithinRadius(
          latitude,
          longitude,
          toy.user.latitude,
          toy.user.longitude,
          Number.MAX_SAFE_INTEGER // Použijeme veľkú hodnotu, aby sme získali skutočnú vzdialenosť
        ) ? calculateDistance(latitude, longitude, toy.user.latitude, toy.user.longitude) : undefined;
      }

      return transformedToy;
    }));

    // Ak máme zoradiť podľa vzdialenosti, zoradíme hračky
    if (sortByDistance && latitude !== undefined && longitude !== undefined) {
      transformedToys.sort((a, b) => {
        // Ak nemáme vzdialenosť pre niektorú hračku, dáme ju na koniec
        if (a.distance === undefined && b.distance === undefined) return 0;
        if (a.distance === undefined) return 1;
        if (b.distance === undefined) return -1;

        // Zoradíme podľa vzdialenosti (od najbližšej po najvzdialenejšiu)
        return a.distance - b.distance;
      });
    }

    return NextResponse.json(transformedToys);
  } catch (error) {
    console.error('Chyba pri získavaní hračiek:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní hračiek' },
      { status: 500 }
    );
  }
}
