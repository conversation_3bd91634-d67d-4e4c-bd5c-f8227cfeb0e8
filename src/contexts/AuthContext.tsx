'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  User,
  signInWithPopup,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  GoogleAuthProvider,
  AuthError,
  updateProfile
} from 'firebase/auth';
import {
  auth,
  googleProvider,
  registerWithEmailAndPassword,
  loginWithEmailAndPassword,
  getLongLivedToken
} from '../lib/firebase';
import { hashId } from '../lib/hashUtils';
import dynamic from 'next/dynamic';

// Dynamicky importujeme modaly, aby sme predišli problémom s SSR
const TermsConfirmationModal = dynamic(() => import('../components/TermsConfirmationModal'), {
  ssr: false,
});

const TermsAcceptanceModal = dynamic(() => import('../components/TermsAcceptanceModal'), {
  ssr: false,
});

// Mapovanie Firebase rolí na existujúce UserRole v databáze
// USER = classic, ADMIN = admin, NONE = none
export type FirebaseUserRole = 'classic' | 'none' | 'admin';

// Rozšírený typ používateľa s rolou
export interface AuthUser extends Partial<User> {
  role?: FirebaseUserRole;
  dbUserId?: number; // ID používateľa v našej databáze
  hashedUserId?: string; // Hashované ID používateľa pre bezpečnejšie API volania
  email?: string | null;
  uid?: string;
  displayName?: string | null;
  photoURL?: string | null;
  getIdToken?: (forceRefresh?: boolean) => Promise<string>;
  termsAccepted?: boolean; // Informácia o akceptácii podmienok
}

// Typ pre kontext autentifikácie
interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  error: string | null;
  signInWithGoogle: () => Promise<void>;
  signInWithEmailAndPassword: (email: string, password: string) => Promise<void>;
  registerWithEmailAndPassword: (email: string, password: string, name: string, termsAccepted?: boolean) => Promise<void>;
  signOut: () => Promise<void>;
  clearError: () => void;
  getLongLivedToken: (user: User) => Promise<string>; // Pridanie funkcie pre získanie tokenu s dlhšou platnosťou
}

// Vytvorenie kontextu
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook pre použitie kontextu
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Provider komponent
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [pendingOAuthUser, setPendingOAuthUser] = useState<User | null>(null);
  const [showTermsAcceptanceModal, setShowTermsAcceptanceModal] = useState(false);
  // Stav na sledovanie, či prebieha proces potvrdzovania podmienok cez OAuth
  const [isConfirmingOAuthTerms, setIsConfirmingOAuthTerms] = useState(false);

  // Funkcia pre kontrolu, či používateľ akceptoval podmienky
  async function checkTermsAcceptance(user: User) {
    try {
      const response = await fetch(`/api/users/email/${encodeURIComponent(user.email || '')}`, {
        headers: {
          'x-auth-request': 'true'
        }
      });

      if (response.ok) {
        const userData = await response.json();
        return userData.termsAccepted === true;
      }
      return false;
    } catch (error) {
      console.error('Chyba pri kontrole akceptácie podmienok:', error);
      return false;
    }
  }

  // Funkcia pre aktualizáciu termsAccepted v databáze
  async function updateTermsAcceptance(userId: number) {
    try {
      const token = await user?.getIdToken?.();
      if (!token) throw new Error('Chýba autentifikačný token');

      // Použitie hashedUserId namiesto číselného ID
      const hashedUserId = user?.hashedUserId;
      if (!hashedUserId) throw new Error('Chýba hashedUserId používateľa');

      console.log('Aktualizujem termsAccepted pre používateľa s hashedId:', hashedUserId);

      const updateResponse = await fetch(`/api/users/${hashedUserId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          termsAccepted: true
        }),
      });

      if (!updateResponse.ok) {
        console.error('Chyba pri aktualizácii súhlasu s podmienkami, status:', updateResponse.status);
        const errorData = await updateResponse.json().catch(() => ({}));
        console.error('Chybová odpoveď:', errorData);
        throw new Error('Nepodarilo sa aktualizovať súhlas s podmienkami');
      }

      return true;
    } catch (error) {
      console.error('Chyba pri aktualizácii súhlasu s podmienkami:', error);
      return false;
    }
  }

  // Funkcia pre kontrolu a nastavenie role používateľa
  async function checkAndSetUserRole(user: User) {
    try {
      // Získanie tokenu s dlhšou platnosťou (7 dní)
      await getLongLivedToken(user);

      // Kontrola, či používateľ existuje v databáze podľa emailu
      // Pridanie hlavičky x-auth-request, aby server vedel, že ide o autentifikačnú požiadavku
      const response = await fetch(`/api/users/email/${encodeURIComponent(user.email || '')}`, {
        headers: {
          'x-auth-request': 'true'
        }
      });

      if (response.ok) {
        // Používateľ existuje v databáze
        const userData = await response.json();

        // Mapovanie role z databázy na FirebaseUserRole
        let firebaseRole: FirebaseUserRole = 'classic';
        if (userData.role === 'ADMIN') {
          firebaseRole = 'admin';
        } else if (userData.role === 'USER') {
          firebaseRole = 'classic';
        } else {
          firebaseRole = 'none';
        }

        // Kontrola, či používateľ nie je blokovaný
        if (userData.status === 'BLOCKED') {
          throw new Error('Váš účet bol zablokovaný. Kontaktujte administrátora.');
        }

        // Nastavenie používateľa s rolou a ďalšími informáciami
        const authUser = user as AuthUser;
        authUser.role = firebaseRole;
        authUser.dbUserId = userData.id;
        authUser.hashedUserId = hashId(userData.id); // Pridanie hashovaného ID

        // Logovanie pre debugovanie - len základné informácie bez citlivých údajov
        console.log('Nastavujem hashedUserId v AuthContext:', authUser.hashedUserId);
        console.log('Používateľ po nastavení ID - základné info:', {
          uid: authUser.uid,
          email: authUser.email,
          displayName: authUser.displayName,
          role: authUser.role,
          dbUserId: authUser.dbUserId,
          hashedUserId: authUser.hashedUserId
        });

        // Ak používateľ nemá nastavené displayName a máme ho v databáze, použijeme meno z databázy
        if (!authUser.displayName && userData.name) {
          Object.defineProperty(authUser, 'displayName', {
            value: userData.name,
            writable: false
          });
        }

        // Ak používateľ nemá nastavený photoURL, môžeme použiť gravatar alebo inú službu
        if (!authUser.photoURL && authUser.email) {
          // Použijeme Gravatar (alebo inú službu pre avatary)
          const emailHash = btoa(authUser.email.toLowerCase().trim());
          Object.defineProperty(authUser, 'photoURL', {
            value: `https://www.gravatar.com/avatar/${emailHash}?d=mp`,
            writable: false
          });
        }

        // Pridanie informácie o termsAccepted do objektu používateľa
        authUser.termsAccepted = userData.termsAccepted === true;

        // Nastavíme používateľa
        setUser(authUser);

        // Ak používateľ nemá akceptované podmienky, zobrazíme modal
        if (!userData.termsAccepted) {
          console.log('Používateľ nemá akceptované podmienky, zobrazujeme modal');
          setShowTermsAcceptanceModal(true);
        } else {
          console.log('Používateľ má akceptované podmienky');
        }
      } else {
        // Používateľ neexistuje v databáze, vytvoríme ho
        // Kontrola, či je to prvý používateľ v systéme
        const countResponse = await fetch('/api/users/count');
        const { count } = await countResponse.json();

        // Určenie role - prvý používateľ dostane rolu 'admin', ostatní 'classic'
        const firebaseRole: FirebaseUserRole = count === 0 ? 'admin' : 'classic';
        const dbRole = firebaseRole === 'admin' ? 'ADMIN' : 'USER';

        // Vytvorenie používateľa v databáze
        const createResponse = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: user.email,
            name: user.displayName || 'Používateľ',
            role: dbRole,
            password: '', // Prázdne heslo, keďže používame Firebase
            termsAccepted: false, // Predvolená hodnota, bude aktualizovaná po potvrdení podmienok
            firebaseUid: user.uid, // Pridanie Firebase UID pre autentifikáciu
          }),
        });

        if (createResponse.ok) {
          const newUser = await createResponse.json();

          // Nastavenie role pre aktuálneho používateľa
          const authUser = user as AuthUser;
          authUser.role = firebaseRole;
          authUser.dbUserId = newUser.id;
          authUser.hashedUserId = hashId(newUser.id); // Pridanie hashovaného ID

          // Logovanie pre debugovanie - len základné informácie bez citlivých údajov
          console.log('Nastavujem hashedUserId v AuthContext (nový používateľ):', authUser.hashedUserId);
          console.log('Používateľ po nastavení ID (nový používateľ) - základné info:', {
            uid: authUser.uid,
            email: authUser.email,
            displayName: authUser.displayName,
            role: authUser.role,
            dbUserId: authUser.dbUserId,
            hashedUserId: authUser.hashedUserId
          });

          // Ak používateľ nemá nastavený photoURL, môžeme použiť gravatar alebo inú službu
          if (!authUser.photoURL && authUser.email) {
            // Použijeme Gravatar (alebo inú službu pre avatary)
            const emailHash = btoa(authUser.email.toLowerCase().trim());
            Object.defineProperty(authUser, 'photoURL', {
              value: `https://www.gravatar.com/avatar/${emailHash}?d=mp`,
              writable: false
            });
          }

          setUser(authUser);
        } else {
          throw new Error('Nepodarilo sa vytvoriť používateľa v databáze');
        }
      }
    } catch (error) {
      console.error('Chyba pri kontrole role používateľa:', error);
      // V prípade chyby nastavíme základnú rolu 'classic'
      const authUser = user as AuthUser;
      authUser.role = 'classic';

      // Ak používateľ nemá nastavený photoURL, môžeme použiť gravatar alebo inú službu
      if (!authUser.photoURL && authUser.email) {
        // Použijeme Gravatar (alebo inú službu pre avatary)
        const emailHash = btoa(authUser.email.toLowerCase().trim());
        Object.defineProperty(authUser, 'photoURL', {
          value: `https://www.gravatar.com/avatar/${emailHash}?d=mp`,
          writable: false
        });
      }

      setUser(authUser);
    }
  }

  // Sledovanie stavu autentifikácie
  useEffect(() => {
    // Nastavíme loading na true na začiatku, aby sme zabezpečili, že aplikácia
    // počká na výsledok autentifikácie
    setLoading(true);

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      try {
        if (user) {
          // Používateľ je prihlásený, nastavíme jeho údaje
          await checkAndSetUserRole(user);
        } else {
          // Používateľ nie je prihlásený, vyčistíme stav
          setUser(null);
        }
      } catch (error) {
        console.error('Chyba pri spracovaní autentifikačného stavu:', error);
        // Aj v prípade chyby nastavíme loading na false, aby sa aplikácia nezasekla
      } finally {
        // Vždy nastavíme loading na false, aby aplikácia vedela, že autentifikačný stav je známy
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, []);

  // Prihlásenie cez Google
  async function signInWithGoogle() {
    try {
      setLoading(true);
      setError(null);
      const result = await signInWithPopup(auth, googleProvider);

      // Kontrola, či používateľ existuje v databáze
      const response = await fetch(`/api/users/email/${encodeURIComponent(result.user.email || '')}`, {
        headers: {
          'x-auth-request': 'true'
        }
      });

      if (response.ok) {
        // Používateľ existuje, aktualizujeme stav
        // Kontrola termsAccepted sa vykoná v checkAndSetUserRole
        await checkAndSetUserRole(result.user);
      } else if (response.status === 404) {
        // Používateľ neexistuje, vytvoríme ho s termsAccepted=false
        await createUserWithoutTermsAccepted(result.user);

        // Používateľ je prihlásený, aktualizujeme stav
        await checkAndSetUserRole(result.user);
      }
    } catch (error) {
      console.error('Chyba pri prihlásení cez Google:', error);
      const authError = error as AuthError;
      setError(`Chyba pri prihlásení cez Google: ${authError.code}`);
    } finally {
      setLoading(false);
    }
  }

  // Pomocná funkcia pre aktualizáciu termsAccepted pre používateľa
  async function updateTermsForUser(user: User, userId: number) {
    try {
      // Získanie tokenu pre autorizáciu
      const token = await user.getIdToken();

      // Aktualizácia používateľa - použitie hashedId namiesto číselného ID
      const hashedUserId = hashId(userId);
      console.log('Automaticky aktualizujem termsAccepted pre používateľa s hashedId:', hashedUserId);

      const updateResponse = await fetch(`/api/users/${hashedUserId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          termsAccepted: true
        }),
      });

      if (!updateResponse.ok) {
        console.error('Chyba pri aktualizácii súhlasu s podmienkami:', await updateResponse.text());
      }
    } catch (error) {
      console.error('Chyba pri aktualizácii súhlasu s podmienkami:', error);
    }
  }

  // Pomocná funkcia pre vytvorenie používateľa s termsAccepted=false
  async function createUserWithoutTermsAccepted(user: User) {
    try {
      // Kontrola, či je to prvý používateľ v systéme
      const countResponse = await fetch('/api/users/count');
      const { count } = await countResponse.json();

      // Určenie role - prvý používateľ dostane rolu 'admin', ostatní 'classic'
      const firebaseRole: FirebaseUserRole = count === 0 ? 'admin' : 'classic';
      const dbRole = firebaseRole === 'admin' ? 'ADMIN' : 'USER';

      // Vytvorenie používateľa v databáze
      const createResponse = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.email,
          name: user.displayName || 'Používateľ',
          role: dbRole,
          password: '', // Prázdne heslo, keďže používame Firebase
          termsAccepted: false, // Nastavíme termsAccepted na false, aby používateľ musel potvrdiť podmienky
          firebaseUid: user.uid, // Pridanie Firebase UID pre autentifikáciu
        }),
      });

      if (!createResponse.ok) {
        console.error('Chyba pri vytváraní používateľa:', await createResponse.text());
      }
    } catch (error) {
      console.error('Chyba pri vytváraní používateľa:', error);
    }
  }

  // Funkcia pre potvrdenie podmienok po OAuth prihlásení
  async function confirmTermsForOAuthUser() {
    if (!pendingOAuthUser) return;

    try {
      setLoading(true);

      // Kontrola, či používateľ existuje v databáze
      const response = await fetch(`/api/users/email/${encodeURIComponent(pendingOAuthUser.email || '')}`, {
        headers: {
          'x-auth-request': 'true'
        }
      });

      if (response.ok) {
        // Používateľ existuje, aktualizujeme termsAccepted
        const userData = await response.json();

        // Získanie tokenu pre autorizáciu
        const token = await pendingOAuthUser.getIdToken();

        // Aktualizácia používateľa - použitie hashedId namiesto číselného ID
        const hashedUserId = hashId(userData.id);
        console.log('Aktualizujem termsAccepted pre OAuth používateľa s hashedId:', hashedUserId);

        const updateResponse = await fetch(`/api/users/${hashedUserId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            termsAccepted: true
          }),
        });

        if (!updateResponse.ok) {
          throw new Error('Nepodarilo sa aktualizovať súhlas s podmienkami');
        }
      } else {
        // Používateľ neexistuje, vytvoríme ho s termsAccepted=true (keďže práve potvrdil podmienky)
        const countResponse = await fetch('/api/users/count');
        const { count } = await countResponse.json();

        // Určenie role - prvý používateľ dostane rolu 'admin', ostatní 'classic'
        const firebaseRole: FirebaseUserRole = count === 0 ? 'admin' : 'classic';
        const dbRole = firebaseRole === 'admin' ? 'ADMIN' : 'USER';

        // Vytvorenie používateľa v databáze
        const createResponse = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: pendingOAuthUser.email,
            name: pendingOAuthUser.displayName || 'Používateľ',
            role: dbRole,
            password: '', // Prázdne heslo, keďže používame Firebase
            termsAccepted: true, // Používateľ potvrdil podmienky v tomto kroku
            firebaseUid: pendingOAuthUser.uid, // Pridanie Firebase UID pre autentifikáciu
          }),
        });

        if (!createResponse.ok) {
          throw new Error('Nepodarilo sa vytvoriť používateľa v databáze');
        }
      }

      // Zatvorenie modalu a vyčistenie stavu
      setShowTermsModal(false);

      // Aktualizácia stavu používateľa, aby sa správne prihlásil
      // Zavoláme checkAndSetUserRole s aktuálnym používateľom, aby sa nastavili všetky potrebné údaje
      await checkAndSetUserRole(pendingOAuthUser);

      setPendingOAuthUser(null);
      setIsConfirmingOAuthTerms(false);

    } catch (error) {
      console.error('Chyba pri potvrdení podmienok:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }

  // Prihlásenie cez email a heslo
  async function signInWithEmailAndPassword(email: string, password: string) {
    try {
      setLoading(true);
      setError(null);
      await loginWithEmailAndPassword(email, password);
    } catch (error) {
      console.error('Chyba pri prihlásení cez email/heslo:', error);
      const authError = error as AuthError;

      // Preklad chybových kódov pre používateľa
      let errorMessage = 'Neznáma chyba pri prihlásení';
      switch (authError.code) {
        case 'auth/invalid-email':
          errorMessage = 'Neplatný formát emailu';
          break;
        case 'auth/user-disabled':
          errorMessage = 'Tento účet bol deaktivovaný';
          break;
        case 'auth/user-not-found':
          errorMessage = 'Používateľ s týmto emailom neexistuje';
          break;
        case 'auth/wrong-password':
          errorMessage = 'Nesprávne heslo';
          break;
        default:
          errorMessage = `Chyba pri prihlásení: ${authError.code}`;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }

  // Registrácia cez email a heslo
  async function registerWithEmailAndPasswordHandler(email: string, password: string, name: string, termsAccepted: boolean = false) {
    try {
      setLoading(true);
      setError(null);
      const user = await registerWithEmailAndPassword(email, password);

      // Nastavenie displayName pre používateľa
      if (user) {
        await updateProfile(user, {
          displayName: name
        });

        // Vytvorenie používateľa v databáze s informáciou o súhlase s podmienkami
        const countResponse = await fetch('/api/users/count');
        const { count } = await countResponse.json();

        // Určenie role - prvý používateľ dostane rolu 'admin', ostatní 'classic'
        const firebaseRole: FirebaseUserRole = count === 0 ? 'admin' : 'classic';
        const dbRole = firebaseRole === 'admin' ? 'ADMIN' : 'USER';

        // Vytvorenie používateľa v databáze
        const createResponse = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: user.email,
            name: name,
            role: dbRole,
            password: '', // Prázdne heslo, keďže používame Firebase
            termsAccepted: termsAccepted, // Uloženie informácie o súhlase
          }),
        });

        if (!createResponse.ok) {
          throw new Error('Nepodarilo sa vytvoriť používateľa v databáze');
        }
      }
    } catch (error) {
      console.error('Chyba pri registrácii:', error);
      const authError = error as AuthError;

      // Preklad chybových kódov pre používateľa
      let errorMessage = 'Neznáma chyba pri registrácii';
      switch (authError.code) {
        case 'auth/email-already-in-use':
          errorMessage = 'Email je už používaný iným účtom';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Neplatný formát emailu';
          break;
        case 'auth/weak-password':
          errorMessage = 'Heslo je príliš slabé';
          break;
        default:
          errorMessage = `Chyba pri registrácii: ${authError.code}`;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }

  // Odhlásenie
  async function signOut() {
    try {
      setLoading(true);
      setError(null);
      await firebaseSignOut(auth);
      setUser(null);
    } catch (error) {
      console.error('Chyba pri odhlásení:', error);
      const authError = error as AuthError;
      setError(`Chyba pri odhlásení: ${authError.code}`);
    } finally {
      setLoading(false);
    }
  }

  // Funkcia pre akceptáciu podmienok
  async function acceptTerms() {
    try {
      setLoading(true);
      setError(null);

      if (!user || !user.dbUserId) {
        throw new Error('Používateľ nie je prihlásený alebo nemá nastavené ID');
      }

      const success = await updateTermsAcceptance(user.dbUserId);

      if (success) {
        // Aktualizujeme lokálny stav používateľa, aby sa modal nezobrazoval znova
        if (user) {
          // Vytvoríme kópiu používateľa s aktualizovaným stavom termsAccepted
          const updatedUser = { ...user, termsAccepted: true };

          // Aktualizujeme používateľa v stave
          setUser(updatedUser);

          // Zatvoríme modal
          setShowTermsAcceptanceModal(false);

          console.log('Súhlas s podmienkami bol úspešne aktualizovaný');
        }
      } else {
        throw new Error('Nepodarilo sa aktualizovať súhlas s podmienkami');
      }
    } catch (error) {
      console.error('Chyba pri akceptácii podmienok:', error);
      setError('Nastala chyba pri akceptácii podmienok. Skúste to znova.');
    } finally {
      setLoading(false);
    }
  }

  // Vyčistenie chyby
  function clearError() {
    setError(null);
  }

  const value = {
    user,
    loading,
    error,
    signInWithGoogle,
    signInWithEmailAndPassword,
    registerWithEmailAndPassword: registerWithEmailAndPasswordHandler,
    signOut,
    clearError,
    getLongLivedToken // Pridanie funkcie pre získanie tokenu s dlhšou platnosťou
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
      {/* Modal pre potvrdenie podmienok po prihlásení */}
      {showTermsAcceptanceModal && user && (
        <TermsAcceptanceModal
          isOpen={showTermsAcceptanceModal}
          onAccept={acceptTerms}
          onSignOut={signOut}
          email={user.email || null}
        />
      )}
    </AuthContext.Provider>
  );
}
