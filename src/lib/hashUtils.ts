/**
 * Utility funkcie pre hashovanie a dehashovanie ID
 * Používa sa na skrytie sekvenčných ID v URL adresách a generovanie unikátnych názvov súborov
 */

import crypto from 'crypto';

// Ta<PERSON><PERSON> k<PERSON> pre hashovanie - v produkcii by mal byť v .env súbore
const SECRET_KEY = process.env.HASH_SECRET_KEY || 'swapka-secret-key-for-toy-id-hashing';

/**
 * Hashuje číselné ID na string, ktorý sa použije v URL
 * @param id Číselné ID na hashovanie
 * @returns Hashovaný string vhodný pre URL
 */
export function hashId(id: number): string {
  if (!id) return '';

  // Konvertujeme ID na string a pridáme tajný kľúč
  const dataToHash = `${id}-${SECRET_KEY}`;

  // Vytvoríme hash pomocou SHA-256
  const hash = crypto.createHash('sha256').update(dataToHash).digest('hex');

  // Vezmeme len prvých 12 znakov hashu, aby URL neboli príliš dlhé
  // a pridáme prefix, aby bolo jasné, že ide o hashované ID
  return `h-${hash.substring(0, 12)}`;
}

/**
 * Konvertuje hashované ID späť na číselné ID
 * Keďže hash je jednosmerný, musíme použiť brute-force prístup
 * a skúšať hashovať postupne ID, kým nenájdeme zhodu
 *
 * @param hashedId Hashované ID z URL
 * @param maxIdToCheck Maximálne ID, ktoré sa má skontrolovať (pre optimalizáciu)
 * @returns Pôvodné číselné ID alebo null, ak sa nenašla zhoda
 */
export function dehashId(hashedId: string, maxIdToCheck: number = 10000): number | null {
  if (!hashedId || !hashedId.startsWith('h-')) return null;

  // Odstránime prefix
  const hashWithoutPrefix = hashedId.substring(2);

  // Skúšame postupne hashovať ID od 1 do maxIdToCheck
  for (let i = 1; i <= maxIdToCheck; i++) {
    const currentHash = hashId(i);
    if (currentHash === hashedId) {
      return i;
    }
  }

  return null;
}

/**
 * Kontroluje, či je string hashované ID
 * @param id String na kontrolu
 * @returns true, ak je string hashované ID, inak false
 */
export function isHashedId(id: string): boolean {
  return Boolean(id) && id.startsWith('h-') && id.length === 14; // "h-" + 12 znakov hashu
}

/**
 * Konvertuje ID (číselné alebo hashované) na číselné ID
 * @param id ID na konverziu (číslo alebo hashovaný string)
 * @returns Číselné ID alebo null, ak sa konverzia nepodarila
 */
export function toNumericId(id: string | number): number | null {
  if (typeof id === 'number') return id;

  // Ak je to hashované ID, dekódujeme ho
  if (isHashedId(id)) {
    return dehashId(id);
  }

  // Ak je to číselné ID ako string, konvertujeme ho
  const numericId = parseInt(id);
  return isNaN(numericId) ? null : numericId;
}

/**
 * Generuje unikátny hash pre názov súboru
 * Používa sa na zabezpečenie, aby sa nezachoval pôvodný názov súboru pri nahrávaní
 *
 * @param originalFilename Pôvodný názov súboru (voliteľné)
 * @param userId ID používateľa (voliteľné)
 * @returns Unikátny hash vhodný pre názov súboru
 */
export function generateUniqueFileHash(originalFilename?: string, userId?: string | number): string {
  // Vytvoríme reťazec, ktorý bude obsahovať aktuálny čas, náhodné číslo,
  // a voliteľne pôvodný názov súboru a ID používateľa
  const timestamp = new Date().getTime();
  const randomValue = Math.random().toString();

  // Vytvoríme reťazec na hashovanie
  let dataToHash = `${timestamp}-${randomValue}`;

  // Pridáme pôvodný názov súboru, ak je k dispozícii
  if (originalFilename) {
    dataToHash += `-${originalFilename}`;
  }

  // Pridáme ID používateľa, ak je k dispozícii
  if (userId) {
    dataToHash += `-${userId}`;
  }

  // Pridáme tajný kľúč pre dodatočnú bezpečnosť
  dataToHash += `-${SECRET_KEY}`;

  // Vytvoríme hash pomocou SHA-256
  const hash = crypto.createHash('sha256').update(dataToHash).digest('hex');

  // Vrátime prvých 16 znakov hashu, čo by malo byť dostatočne unikátne
  return hash.substring(0, 16);
}

/**
 * Generuje bezpečný hash pre názov súboru s rozšírením
 * Táto funkcia je špecificky navrhnutá pre bezpečné ukladanie súborov
 *
 * @param originalFilename Pôvodný názov súboru
 * @param userId ID používateľa
 * @param fileExtension Prípona súboru (napr. 'webp', 'jpg')
 * @returns Objekt s hashovaným názvom súboru a metadátami
 */
export function generateSecureFilename(
  originalFilename: string,
  userId: string | number,
  fileExtension: string
): {
  hashedFilename: string;
  publicId: string;
  fullPath: string;
} {
  // Vytvoríme timestamp s vysokou presnosťou
  const timestamp = Date.now();
  const nanoTime = process.hrtime.bigint().toString();

  // Vytvoríme kryptograficky bezpečný náhodný reťazec
  const randomBytes = crypto.randomBytes(16).toString('hex');

  // Sanitizujeme pôvodný názov súboru (odstránime nebezpečné znaky)
  const sanitizedOriginal = originalFilename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .substring(0, 50); // Obmedzíme dĺžku

  // Vytvoríme reťazec na hashovanie
  const dataToHash = [
    timestamp,
    nanoTime,
    randomBytes,
    sanitizedOriginal,
    userId,
    SECRET_KEY
  ].join('-');

  // Vytvoríme SHA-256 hash
  const hash = crypto.createHash('sha256').update(dataToHash).digest('hex');

  // Vytvoríme URL-safe hash (použijeme base64url encoding)
  const urlSafeHash = crypto
    .createHash('sha256')
    .update(dataToHash)
    .digest('base64url')
    .substring(0, 24); // 24 znakov pre dostatočnú entropiu

  // Normalizujeme príponu súboru
  const normalizedExtension = fileExtension.toLowerCase().replace(/^\./, '');

  // Vytvoríme finálny hashovaný názov súboru
  const hashedFilename = `${urlSafeHash}.${normalizedExtension}`;

  // Vytvoríme public_id pre Cloudinary (bez prípony)
  const publicId = urlSafeHash;

  // Vytvoríme plnú cestu pre Cloudinary
  const fullPath = `swapka/toys/${publicId}`;

  return {
    hashedFilename,
    publicId,
    fullPath
  };
}
